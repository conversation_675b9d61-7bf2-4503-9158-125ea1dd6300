<?php

namespace Modules\Flight\Controllers;

use App;
use App\Http\Controllers\Controller;
use Modules\Flight\Models\SeatType;
use Modules\Location\Models\LocationCategory;
use Modules\Flight\Models\Flight;
use Illuminate\Http\Request;
use Modules\Location\Models\Location;
use Modules\Review\Models\Review;
use Modules\Core\Models\Attributes;
use DB;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use DOMDocument;
use App\Services\Travelport\LowFareSearchService;
use Carbon\Carbon;

class FlightController extends Controller
{
    protected $flightClass;
    protected $locationClass;
    /**
     * @var string
     */
    private $locationCategoryClass;

    public function __construct(Flight $flightClass, Location $locationClass)
    {
        $this->flightClass = $flightClass;
        $this->locationClass = $locationClass;
    }

    public function callAction($method, $parameters)
    {
        if(!Flight::isEnable()) {
            return redirect('/');
        }
        return parent::callAction($method, $parameters); // TODO: Change the autogenerated stub
    }

    public function index(Request $request)
    {

        $is_ajax = $request->query('_ajax');

        if(!empty($request->query('limit'))) {
            $limit = $request->query('limit');
        } else {
            $limit = !empty(setting_item("flight_page_limit_item")) ? setting_item("flight_page_limit_item") : 9;
        }
        $query = $this->flightClass->search($request->input());
        $list = $query->paginate($limit);
        $markers = [];
        if (!empty($list)) {
            foreach ($list as $row) {
                $markers[] = [
                    "id"      => $row->id,
                    "title"   => $row->title,
                    "lat"     => (float)$row->map_lat,
                    "lng"     => (float)$row->map_lng,
                    "gallery" => $row->getGallery(true),
                    "infobox" => view('Flight::frontend.layouts.search.loop-grid', ['row' => $row,'disable_lazyload' => 1,'wrap_class' => 'infobox-item'])->render(),
                    'marker' => get_file_url(setting_item("flight_icon_marker_map"), 'full') ?? url('images/icons/png/pin.png'),
                ];
            }
        }
        $limit_location = 15;
        if(empty(setting_item("flight_location_search_style")) or setting_item("flight_location_search_style") == "normal") {
            $limit_location = 1000;
        }
        $data = [
            'rows'               => $list,
            'list_location'      => $this->locationClass::where('status', 'publish')->limit($limit_location)->with(['translation'])->get()->toTree(),
            'seatType'           => SeatType::get(),
            'flight_min_max_price' => $this->flightClass::getMinMaxPrice(),
            'markers'            => $markers,
            "blank" => setting_item('search_open_tab') == "current_tab" ? 0 : 1 ,
            "seo_meta"           => $this->flightClass::getSeoMetaForPageList()
        ];
        $data['flights'] = $this->searchFlight();
        $layout = setting_item("flight_layout_search", 'normal');
        if ($request->query('_layout')) {
            $layout = $request->query('_layout');
        }
        if ($is_ajax) {
            return $this->sendSuccess([
                'html'    => view('Flight::frontend.layouts.search-map.list-item', $data)->render(),
                "markers" => $data['markers']
            ]);
        }
        $data['attributes'] = Attributes::where('service', 'flight')->orderBy("position", "desc")->with(['terms' => function ($query) {
            $query->withCount('flight');
        },'translation'])->get();

        if ($layout == "map") {
            $data['body_class'] = 'has-search-map';
            $data['html_class'] = 'full-page';
            return view('Flight::frontend.search-map', $data);
        }

        return view('Flight::frontend.search', $data);
    }

    public function getData(Request $request, $id)
    {
        $row = $this->flightClass::with(['flightSeat.seatType','airportFrom','airportTo','airline','bookingPassengers'])->find($id);
        if (empty($row)) {
            return $this->sendError('no found');
        } else {
            if(!empty($row->airline)) {
                $row->airline->append(['image_url']);
            }
            $bookingPassengers = $row->bookingPassengers->countBy('seat_type')->toArray();
            if(!empty($row->flightSeat)) {
                foreach ($row->flightSeat as &$value) {
                    if(!empty($bookingPassengers[$value->seat_type])) {
                        $value->max_passengers = $value->max_passengers - $bookingPassengers[$value->seat_type];
                        if($value->max_passengers < 0) {
                            $value->max_passengers = 0;
                        }
                    }
                    $value->price_html = format_money($value->price);
                    $value->number = 0;
                }
            }
            $row->departure_time_html = $row->departure_time->format('H:i');
            $row->departure_date_html = $row->departure_time->format('D, d M y');
            $row->arrival_time_html = $row->arrival_time->format('H:i');
            $row->arrival_date_html = $row->arrival_time->format('D, d M y');

            return $this->sendSuccess(['data' => $row->toArray()], 'founded');
        }
    }

    private function searchFlight()
    {
        $service = new LowFareSearchService();

        // Handle different date formats and trip types
        $start = null;
        $end = null;

        // Determine trip type and get appropriate dates
        if (request()->has('start_roundtrip')) {
            // Round trip: start_oneway = departure, start_roundtrip = return
            $start = $this->parseDate(request()->input('start_oneway'));
            $end = $this->parseDate(request()->input('start_roundtrip'));
        } elseif (request()->has('start_multiway') || (request()->has('start_oneway') && is_array(request()->input('start_oneway')))) {
            // Multi-city: use start_oneway array
            $startOneway = request()->input('start_oneway');
            if (is_array($startOneway)) {
                $start = $this->parseDate($startOneway[0] ?? null);
            } else {
                $start = $this->parseDate($startOneway);
            }
        } else {
            // One-way: start_oneway = departure date
            $start = $this->parseDate(request()->input('start_oneway') ?? request()->input('start'));
            // Check for legacy end date
            if (request()->has('end')) {
                $end = $this->parseDate(request()->input('end'));
            }
        }

        // Handle different trip types
        $origin = request()->input('from_where');
        $destination = request()->input('to_where');
        $segments = null;

        \Log::info('Flight search input analysis', [
            'origin' => $origin,
            'destination' => $destination,
            'origin_type' => gettype($origin),
            'destination_type' => gettype($destination),
            'has_start_multiway' => request()->has('start_multiway'),
            'start_multiway_value' => request()->input('start_multiway'),
            'multi_city_data' => request()->input('multi_city', [])
        ]);

        // Check for multi-city segments - FIXED: Check start_multiway value, not just existence
        $isMultiCity = (request()->input('start_multiway') == '1') || is_array($origin) || is_array($destination);

        if ($isMultiCity) {
            \Log::info('Multi-city search detected', [
                'start_multiway_value' => request()->input('start_multiway'),
                'origin_is_array' => is_array($origin),
                'destination_is_array' => is_array($destination)
            ]);

            $segments = $this->buildMultiCitySegments();

            // For multi-city, we need to search each segment individually
            if (!empty($segments) && count($segments) > 1) {
                \Log::info('Calling searchMultiCityFlights with segments', [
                    'segments_count' => count($segments),
                    'segments' => $segments
                ]);
                return $this->searchMultiCityFlights($segments);
            }

            \Log::warning('Multi-city detected but insufficient segments built', [
                'segments_count' => count($segments),
                'segments' => $segments
            ]);

            // Fallback to first segment if segments building failed
            if (is_array($origin)) $origin = $origin[0] ?? null;
            if (is_array($destination)) $destination = $destination[0] ?? null;
        } else {
            // Handle array format for single trip
            if (is_array($origin)) $origin = $origin[0] ?? null;
            if (is_array($destination)) $destination = $destination[0] ?? null;
        }

        // Fix travelers parameter - convert seat_type array to proper format
        $travelers = $this->formatTravelers(request()->input('seat_type', []));

        // Get cabin class and direct flight preferences
        $cabinClass = $this->getCabinClass();
        $directFlight = request()->input('direct_flight') == '1';

        $searchParams = [
            'origin' => $origin ?: 'DXB',
            'destination' => $destination ?: 'CAI',
            'dep_date' => $start ? $start->format('Y-m-d') : now()->addDays(1)->format('Y-m-d'),
            'return_date' => $end ? $end->format('Y-m-d') : null,
            'travelers' => $travelers,
            'class' => $cabinClass,
            'direct_flight' => $directFlight
        ];

        // Add segments for multi-city
        if ($segments) {
            $searchParams['segments'] = $segments;
        }

        // Add debug logging
        \Log::info('Flight search parameters', [
            'search_params' => $searchParams,
            'request_data' => request()->all(),
            'trip_type_detected' => request()->has('start_roundtrip') ? 'round_trip' : (request()->has('start_multiway') ? 'multi_city' : 'one_way')
        ]);

        $response = $service->searchFlights($searchParams);

        // Clear multi-city segments from session for non-multi-city searches
        if (!$isMultiCity) {
            session()->forget('multi_city_segments');
        }

        return $response;
    }

    /**
     * Parse date from various formats
     */
    private function parseDate($dateString)
    {
        if (!$dateString) {
            return null;
        }

        try {
            // Try d/m/Y format first
            if (strpos($dateString, '/') !== false) {
                return Carbon::createFromFormat('d/m/Y', $dateString);
            }

            // Try Y-m-d format
            if (strpos($dateString, '-') !== false) {
                return Carbon::createFromFormat('Y-m-d', $dateString);
            }

            // Try to parse as general date
            return Carbon::parse($dateString);
        } catch (\Exception $e) {
            // Return tomorrow as fallback
            return Carbon::tomorrow();
        }
    }

    /**
     * Format travelers from URL parameters or seat_type array to proper format
     */
    private function formatTravelers($seatType = [])
    {
        // Default travelers
        $travelers = ['adult' => 1, 'child' => 0, 'infant' => 0];

        // First, check for direct URL parameters (adults, children, infants)
        $adults = request()->input('adults');
        $children = request()->input('children');
        $infants = request()->input('infants');

        if ($adults !== null || $children !== null || $infants !== null) {
            // Use URL parameters directly
            $travelers['adult'] = max(1, (int)($adults ?? 1));
            $travelers['child'] = max(0, (int)($children ?? 0));
            $travelers['infant'] = max(0, (int)($infants ?? 0));
        } elseif (is_array($seatType)) {
            // Handle different possible formats from seat_type array
            foreach ($seatType as $key => $value) {
                if (is_numeric($value) && $value > 0) {
                    switch (strtolower($key)) {
                        case 'adults':
                        case 'adult':
                        case 'adt':
                            $travelers['adult'] = (int)$value;
                            break;
                        case 'children':
                        case 'child':
                        case 'chd':
                        case 'cnn':
                            $travelers['child'] = (int)$value;
                            break;
                        case 'infants':
                        case 'infant':
                        case 'inf':
                            $travelers['infant'] = (int)$value;
                            break;
                    }
                }
            }
        } elseif (is_string($seatType)) {
            // Handle string format like "1,0,0" (adults,children,infants)
            $parts = explode(',', $seatType);
            if (count($parts) >= 1) $travelers['adult'] = (int)($parts[0] ?? 1);
            if (count($parts) >= 2) $travelers['child'] = (int)($parts[1] ?? 0);
            if (count($parts) >= 3) $travelers['infant'] = (int)($parts[2] ?? 0);
        }

        // Ensure at least 1 adult
        if ($travelers['adult'] < 1) {
            $travelers['adult'] = 1;
        }

        return $travelers;
    }

    /**
     * Build multi-city segments from request data
     */
    private function buildMultiCitySegments()
    {
        $segments = [];

        // Get arrays of origins, destinations, and dates based on actual form structure
        $origins = request()->input('from_where', []);
        $destinations = request()->input('to_where', []);
        $dates = request()->input('start_oneway', []); // Multi-city uses start_oneway array

        // Ensure we have arrays for the standard structure
        if (!is_array($origins)) $origins = [$origins];
        if (!is_array($destinations)) $destinations = [$destinations];
        if (!is_array($dates)) $dates = [$dates];

        // Build segments from the standard arrays (from_where[0], to_where[0], etc.)
        $maxSegments = max(count($origins), count($destinations), count($dates));

        for ($i = 0; $i < $maxSegments; $i++) {
            $origin = $origins[$i] ?? null;
            $destination = $destinations[$i] ?? null;
            $date = $dates[$i] ?? null;

            if ($origin && $destination && $date) {
                $parsedDate = $this->parseDate($date);
                if ($parsedDate) {
                    $segments[] = [
                        'origin' => $origin,
                        'destination' => $destination,
                        'date' => $parsedDate->format('Y-m-d')
                    ];
                }
            }
        }

        // Also process multi_city structure (from dynamic form additions)
        $multiCityData = request()->input('multi_city', []);
        if (!empty($multiCityData)) {
            foreach ($multiCityData as $segment) {
                if (isset($segment['from_where'], $segment['to_where'], $segment['start_date'])) {
                    $parsedDate = $this->parseDate($segment['start_date']);
                    if ($parsedDate) {
                        $segments[] = [
                            'origin' => $segment['from_where'],
                            'destination' => $segment['to_where'],
                            'date' => $parsedDate->format('Y-m-d')
                        ];
                    }
                }
            }
        }

        // Add debug logging
        \Log::info('Multi-city segments built', [
            'segments_count' => count($segments),
            'segments' => $segments,
            'raw_origins' => $origins,
            'raw_destinations' => $destinations,
            'raw_dates' => $dates,
            'multi_city_data' => $multiCityData
        ]);

        return $segments;
    }

    /**
     * Get cabin class from request
     */
    private function getCabinClass()
    {
        $seatType = request()->input('seat_type');

        // If seat_type is a string (from hidden input), use it directly
        if (is_string($seatType)) {
            return $this->mapCabinClass($seatType);
        }

        // If seat_type is an array, look for class information
        if (is_array($seatType) && isset($seatType['class'])) {
            return $this->mapCabinClass($seatType['class']);
        }

        // Check for direct class parameter
        $class = request()->input('class', request()->input('cabin_class', 'economy'));
        return $this->mapCabinClass($class);
    }

    /**
     * Map cabin class to TravelPort format
     */
    private function mapCabinClass($class)
    {
        $mapping = [
            'economy' => 'Economy',
            'premium' => 'PremiumEconomy',
            'business' => 'Business',
            'first' => 'First'
        ];

        return $mapping[strtolower($class)] ?? 'Economy';
    }

    /**
     * Search flights for multi-city trips - FlyWT style (segment-based display)
     */
    private function searchMultiCityFlights($segments)
    {
        $service = new LowFareSearchService();
        $segmentResults = [];

        // Get common search parameters
        $travelers = $this->formatTravelers(request()->input('seat_type', []));
        $cabinClass = $this->getCabinClass();
        $directFlight = request()->input('direct_flight') == '1';

        // Search each segment individually and group results by segment
        foreach ($segments as $index => $segment) {
            $searchParams = [
                'origin' => $segment['origin'],
                'destination' => $segment['destination'],
                'dep_date' => $segment['date'],
                'return_date' => null, // Multi-city segments are one-way
                'travelers' => $travelers,
                'class' => $cabinClass,
                'direct_flight' => $directFlight
            ];

            try {
                $results = $service->searchFlights($searchParams);

                // Add segment information to each result
                foreach ($results as &$result) {
                    $result['segment_index'] = $index;
                    $result['segment_info'] = [
                        'origin' => $segment['origin'],
                        'destination' => $segment['destination'],
                        'date' => $segment['date'],
                        'segment_number' => $index + 1,
                        'total_segments' => count($segments)
                    ];
                    $result['is_multi_city_segment'] = true;
                }

                // Helper function to get airport name from code
                $getAirportName = function($code) {
                    // Try exact code match first
                    $airport = \App\Models\Airports::where('code', $code)->first();

                    // If no exact match, try partial name match
                    if (!$airport) {
                        $airport = App\Models\Airports::where('name', 'like', '%' . $code . '%')->first();
                    }

                    return $airport ? $airport->name : $code;
                };

                // Get airport names for better display
                $originName = $getAirportName($segment['origin']);
                $destinationName = $getAirportName($segment['destination']);

                // Group results by segment (FlyWT style)
                $segmentResults[$index] = [
                    'segment_info' => $segment,
                    'segment_number' => $index + 1,
                    'flights' => $results,
                    'route' => $originName . ' → ' . $destinationName,
                    'date' => $segment['date']
                ];

            } catch (\Exception $e) {
                \Log::error('Multi-city segment search failed', [
                    'segment' => $segment,
                    'error' => $e->getMessage()
                ]);

                // Add empty results for failed segment to maintain structure
                $segmentResults[$index] = [
                    'segment_info' => $segment,
                    'segment_number' => $index + 1,
                    'flights' => [],
                    'route' => $segment['origin'] . ' → ' . $segment['destination'],
                    'date' => $segment['date'],
                    'error' => $e->getMessage()
                ];
            }
        }

        // Add debug logging for multi-city
        \Log::info('Multi-city flight search completed (FlyWT style)', [
            'segments_count' => count($segments),
            'segment_results_structure' => array_map(function($seg) {
                return [
                    'segment_number' => $seg['segment_number'],
                    'route' => $seg['route'],
                    'date' => $seg['date'],
                    'flights_count' => count($seg['flights']),
                    'has_error' => isset($seg['error'])
                ];
            }, $segmentResults),
            'segments' => $segments
        ]);

        // Store segment results in session for FlyWT-style display
        session(['multi_city_segments' => $segmentResults]);

        // Return empty array since we'll display segments separately
        return [];
    }

    /**
     * Generate all possible combinations of flights for multi-city trips
     */
    private function generateMultiCityTripCombinations($segmentResults, $segments)
    {
        // If any segment has no flights, return empty array
        foreach ($segmentResults as $results) {
            if (empty($results)) {
                return [];
            }
        }

        $combinations = [];
        $segmentCount = count($segmentResults);

        // Generate combinations using recursive approach
        $this->generateCombinations($segmentResults, $segments, [], 0, $combinations);

        // Limit combinations to prevent overwhelming the user (max 100 combinations)
        if (count($combinations) > 100) {
            $combinations = array_slice($combinations, 0, 100);
            \Log::info('Multi-city combinations limited to 100', [
                'total_possible' => count($combinations),
                'displayed' => 100
            ]);
        }

        return $combinations;
    }

    /**
     * Recursive function to generate flight combinations
     */
    private function generateCombinations($segmentResults, $segments, $currentCombination, $segmentIndex, &$combinations)
    {
        // Base case: if we've selected flights for all segments
        if ($segmentIndex >= count($segmentResults)) {
            $combinations[] = $this->createCombinedTrip($currentCombination, $segments);
            return;
        }

        // Get flights for current segment
        $currentSegmentFlights = $segmentResults[$segmentIndex];

        // For each flight in current segment, recurse to next segment
        foreach ($currentSegmentFlights as $flight) {
            $newCombination = $currentCombination;
            $newCombination[] = $flight;

            $this->generateCombinations($segmentResults, $segments, $newCombination, $segmentIndex + 1, $combinations);
        }
    }

    /**
     * Create a combined trip from individual flight segments
     */
    private function createCombinedTrip($flightCombination, $segments)
    {
        $totalPrice = 0;
        $allJourneys = [];
        $allPricingInfo = [];
        $passengerCounts = [];

        // Combine all segments into one trip
        foreach ($flightCombination as $index => $flight) {
            // Add segment information
            if (isset($flight['journeys'])) {
                foreach ($flight['journeys'] as $journey) {
                    // Mark each journey with segment info
                    $journey['segment_index'] = $index;
                    $journey['segment_info'] = [
                        'origin' => $segments[$index]['origin'],
                        'destination' => $segments[$index]['destination'],
                        'date' => $segments[$index]['date'],
                        'segment_number' => $index + 1
                    ];
                    $allJourneys[] = $journey;
                }
            }

            // Combine pricing info
            if (isset($flight['pricing_info'])) {
                $allPricingInfo = array_merge($allPricingInfo, $flight['pricing_info']);
            }

            // Add to total price
            if (isset($flight['total_price'])) {
                $priceValue = floatval(str_replace(['$', ',', 'EGP'], '', $flight['total_price']));
                $totalPrice += $priceValue;
            }

            // Get passenger counts from first flight
            if ($index === 0 && isset($flight['passenger_counts'])) {
                $passengerCounts = $flight['passenger_counts'];
            }
        }

        // Create combined trip structure
        $combinedTrip = [
            'is_multi_city' => true,
            'total_segments' => count($segments),
            'journeys' => $allJourneys,
            'pricing_info' => $allPricingInfo,
            'passenger_counts' => $passengerCounts,
            'total_price' => number_format($totalPrice, 2) . ' EGP',
            'segment_flights' => $flightCombination, // Keep individual flights for reference
            'segments_info' => $segments
        ];

        return $combinedTrip;
    }
}

